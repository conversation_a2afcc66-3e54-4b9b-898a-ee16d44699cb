//
//  CycleSize.swift
//  Rectangle Integration for OhSnap
//
//  Simplified cycle size system for Rectangle functionality
//

import Foundation

// MARK: - CycleSize

struct CycleSize: Codable, Equatable {
    let width: Float
    let height: Float

    init(width: Float, height: Float) {
        self.width = width
        self.height = height
    }

    static let half = CycleSize(width: 0.5, height: 1.0)
    static let twoThirds = CycleSize(width: 0.67, height: 1.0)
    static let oneThird = CycleSize(width: 0.33, height: 1.0)

    static let defaultSizes: [CycleSize] = [
        .half,
        .twoThirds,
        .oneThird,
    ]
}

// MARK: - Logger

class Logger {
    static var logging: Bool = false

    static func log(_ message: String) {
        if logging {
            print("[Rectangle] \(message)")
        }
    }
}

// MARK: - SubWindowAction

enum SubWindowAction: Int, Codable {
    case leftHalf = 0
    case rightHalf = 1
    case topHalf = 2
    case bottomHalf = 3
    case topLeft = 4
    case topRight = 5
    case bottomLeft = 6
    case bottomRight = 7
    case leftThird = 8
    case rightThird = 9
    case centerThird = 10
    case leftTwoThirds = 11
    case rightTwoThirds = 12
    case topThird = 13
    case bottomThird = 14
    case topTwoThirds = 15
    case bottomTwoThirds = 16
    case leftThreeFourths = 17
    case rightThreeFourths = 18
    case topThreeFourths = 19
    case bottomThreeFourths = 20
    case bottomRightEighth = 21
    case topLeftEighth = 22
    case topCenterLeftEighth = 23
    case topCenterRightEighth = 24
    case topRightEighth = 25
    case bottomLeftEighth = 26
    case bottomCenterLeftEighth = 27
    case bottomCenterRightEighth = 28
}
