//
//  TodoManager.swift
//  Rectangle Integration for OhSnap
//
//  Simplified Todo manager for Rectangle functionality
//

import Foundation

// MARK: - TodoManager

class TodoManager {
    
    /// Checks if a window is a Todo window (Stage Manager related)
    /// For OhSnap, we'll keep this simple and return false for now
    static func isTodoWindow(_ windowId: CGWindowID) -> Bool {
        // In Rectangle, this checks for Stage Manager windows
        // For OhSnap, we'll disable this feature for now
        return false
    }
    
    /// Gets the stage size for Todo windows
    static func getStageSize() -> CGFloat {
        return Defaults.stageSize.floatValue
    }
    
    /// Checks if Todo mode is enabled
    static var todoEnabled: Bool {
        return Defaults.todo.enabled
    }
    
    /// Checks if we should always account for stage
    static var alwaysAccountForStage: Bool {
        return Defaults.alwaysAccountForStage.enabled
    }
}

// MARK: - GapCalculation

class GapCalculation {
    
    /// Applies gaps to a rectangle based on the gap settings
    static func applyGaps(
        _ rect: CGRect,
        dimension: GapApplicability,
        sharedEdges: GapSharedEdge,
        gapSize: Int
    ) -> CGRect {
        
        guard gapSize > 0 else { return rect }
        
        var adjustedRect = rect
        let gap = CGFloat(gapSize)
        
        switch dimension {
        case .both:
            // Apply gaps to both width and height
            if !sharedEdges.contains(.top) {
                adjustedRect.origin.y += gap
                adjustedRect.size.height -= gap
            }
            if !sharedEdges.contains(.bottom) {
                adjustedRect.size.height -= gap
            }
            if !sharedEdges.contains(.left) {
                adjustedRect.origin.x += gap
                adjustedRect.size.width -= gap
            }
            if !sharedEdges.contains(.right) {
                adjustedRect.size.width -= gap
            }
            
        case .width:
            // Apply gaps only to width
            if !sharedEdges.contains(.left) {
                adjustedRect.origin.x += gap
                adjustedRect.size.width -= gap
            }
            if !sharedEdges.contains(.right) {
                adjustedRect.size.width -= gap
            }
            
        case .height:
            // Apply gaps only to height
            if !sharedEdges.contains(.top) {
                adjustedRect.origin.y += gap
                adjustedRect.size.height -= gap
            }
            if !sharedEdges.contains(.bottom) {
                adjustedRect.size.height -= gap
            }
            
        case .none:
            // No gaps to apply
            break
        }
        
        return adjustedRect
    }
}

// MARK: - GapApplicability

enum GapApplicability {
    case none
    case width
    case height
    case both
}

// MARK: - GapSharedEdge

struct GapSharedEdge: OptionSet {
    let rawValue: Int
    
    static let top = GapSharedEdge(rawValue: 1 << 0)
    static let bottom = GapSharedEdge(rawValue: 1 << 1)
    static let left = GapSharedEdge(rawValue: 1 << 2)
    static let right = GapSharedEdge(rawValue: 1 << 3)
    
    static let none: GapSharedEdge = []
    static let all: GapSharedEdge = [.top, .bottom, .left, .right]
}

// MARK: - NSScreen Extensions

extension NSScreen {
    
    /// Gets the adjusted visible frame accounting for Todo/Stage Manager
    func adjustedVisibleFrame(_ ignoreTodo: Bool = false) -> CGRect {
        var adjustedFrame = visibleFrame
        
        // Apply screen edge gaps if enabled
        if Defaults.screenEdgeGapsOnMainScreenOnly.enabled {
            // Only apply to main screen
            if self == NSScreen.main {
                adjustedFrame = applyScreenEdgeGaps(to: adjustedFrame)
            }
        } else {
            // Apply to all screens
            adjustedFrame = applyScreenEdgeGaps(to: adjustedFrame)
        }
        
        // Handle Todo/Stage Manager if not ignored
        if !ignoreTodo && TodoManager.todoEnabled {
            let stageSize = TodoManager.getStageSize()
            if stageSize > 0 {
                adjustedFrame.size.width -= stageSize
            }
        }
        
        return adjustedFrame
    }
    
    private func applyScreenEdgeGaps(to frame: CGRect) -> CGRect {
        var adjustedFrame = frame
        
        let topGap = CGFloat(Defaults.screenEdgeGapTop.intValue)
        let bottomGap = CGFloat(Defaults.screenEdgeGapBottom.intValue)
        let leftGap = CGFloat(Defaults.screenEdgeGapLeft.intValue)
        let rightGap = CGFloat(Defaults.screenEdgeGapRight.intValue)
        
        adjustedFrame.origin.x += leftGap
        adjustedFrame.origin.y += bottomGap
        adjustedFrame.size.width -= (leftGap + rightGap)
        adjustedFrame.size.height -= (topGap + bottomGap)
        
        return adjustedFrame
    }
}

// MARK: - CGRect Extensions

extension CGRect {
    
    /// Returns the center point of the rectangle
    var centerPoint: CGPoint {
        return CGPoint(x: midX, y: midY)
    }
    
    /// Flips the Y coordinate for the AX coordinate system
    var screenFlipped: CGRect {
        guard !isNull else {
            return self
        }
        guard let mainScreen = NSScreen.main else { return self }
        let mainScreenHeight = mainScreen.frame.height
        var flipped = self
        flipped.origin.y = mainScreenHeight - self.origin.y - self.height
        return flipped
    }
    
    /// Returns true if the rectangle is wider than it is tall
    var isLandscape: Bool { 
        width > height 
    }
}

// MARK: - Logger Compatibility

extension Logger {
    static func log(_ message: String) {
        LoggingService.shared.debug(message, service: "Rectangle")
    }
}
