//
//  Defaults.swift
//  Rectangle Integration for OhSnap
//
//  Rectangle's exact defaults system adapted for OhSnap
//

import Foundation

// MARK: - CodableDefault

struct CodableDefault: Codable {
    let bool: Bool?
    let int: Int?
    let float: Float?
    let string: String?

    init(bool: Bool? = nil, int: Int? = nil, float: Float? = nil, string: String? = nil) {
        self.bool = bool
        self.int = int
        self.float = float
        self.string = string
    }
}

// MARK: - Default Protocol

protocol Default {
    var key: String { get }
    func load(from codable: CodableDefault)
    func toCodable() -> CodableDefault
}

// MARK: - BoolDefault

class BoolDefault: Default {
    public private(set) var key: String
    private var initialized = false

    var enabled: Bool {
        didSet {
            if initialized {
                UserDefaults.standard.set(enabled, forKey: key)
            }
        }
    }

    init(key: String) {
        self.key = key
        enabled = UserDefaults.standard.bool(forKey: key)
        initialized = true
    }

    func load(from codable: CodableDefault) {
        if let value = codable.bool {
            self.enabled = value
        }
    }

    func toCodable() -> CodableDefault {
        return CodableDefault(bool: enabled)
    }
}

// MARK: - OptionalBoolDefault

class OptionalBoolDefault: Default {
    public private(set) var key: String
    private var initialized = false

    var enabled: Bool? {
        didSet {
            if initialized {
                if let enabled = enabled {
                    UserDefaults.standard.set(enabled, forKey: key)
                } else {
                    UserDefaults.standard.removeObject(forKey: key)
                }
            }
        }
    }

    var userEnabled: Bool {
        return enabled ?? true
    }

    var userDisabled: Bool {
        return enabled == false
    }

    init(key: String) {
        self.key = key
        if UserDefaults.standard.object(forKey: key) != nil {
            enabled = UserDefaults.standard.bool(forKey: key)
        }
        initialized = true
    }

    private func set(using intValue: Int) {
        switch intValue {
        case 1:
            enabled = true
        case 2:
            enabled = false
        default:
            enabled = nil
        }
    }

    func load(from codable: CodableDefault) {
        if let value = codable.int {
            set(using: value)
        }
    }

    func toCodable() -> CodableDefault {
        guard let enabled = enabled else { return CodableDefault(int: 0) }
        let intValue = enabled ? 1 : 2
        return CodableDefault(int: intValue)
    }
}

// MARK: - FloatDefault

class FloatDefault: Default {
    public private(set) var key: String
    private var initialized = false

    var value: Float {
        didSet {
            if initialized {
                UserDefaults.standard.set(value, forKey: key)
            }
        }
    }

    var cgFloat: CGFloat { CGFloat(value) }

    init(key: String, defaultValue: Float = 0) {
        self.key = key
        value = UserDefaults.standard.float(forKey: key)
        if defaultValue != 0 && value == 0 {
            value = defaultValue
        }
        initialized = true
    }

    func load(from codable: CodableDefault) {
        if let float = codable.float {
            value = float
        }
    }

    func toCodable() -> CodableDefault {
        return CodableDefault(float: value)
    }
}

// MARK: - IntDefault

class IntDefault: Default {
    public private(set) var key: String
    private var initialized = false

    var value: Int {
        didSet {
            if initialized {
                UserDefaults.standard.set(value, forKey: key)
            }
        }
    }

    init(key: String, defaultValue: Int = 0) {
        self.key = key
        value = UserDefaults.standard.integer(forKey: key)
        if defaultValue != 0 && value == 0 {
            value = defaultValue
        }
        initialized = true
    }

    func load(from codable: CodableDefault) {
        if let int = codable.int {
            value = int
        }
    }

    func toCodable() -> CodableDefault {
        return CodableDefault(int: value)
    }
}

// MARK: - SubsequentExecutionMode

enum SubsequentExecutionMode: Int {
    case resize = 0
    case acrossMonitor = 1
    case none = 2
    case acrossAndResize = 3
    case cycleMonitor = 4

    var resizes: Bool {
        switch self {
        case .resize, .acrossAndResize:
            return true
        default:
            return false
        }
    }
}

// MARK: - SubsequentExecutionDefault

class SubsequentExecutionDefault: Default {
    public private(set) var key: String = "subsequentExecutionMode"
    private var initialized = false

    var value: SubsequentExecutionMode {
        didSet {
            if initialized {
                UserDefaults.standard.set(value.rawValue, forKey: key)
            }
        }
    }

    init() {
        let intValue = UserDefaults.standard.integer(forKey: key)
        value = SubsequentExecutionMode(rawValue: intValue) ?? .resize
        initialized = true
    }

    func load(from codable: CodableDefault) {
        if let intValue = codable.int {
            value = SubsequentExecutionMode(rawValue: intValue) ?? .resize
        }
    }

    func toCodable() -> CodableDefault {
        return CodableDefault(int: value.rawValue)
    }
}

// MARK: - Defaults Container

class Defaults {

    // MARK: - Window Management
    static let subsequentExecutionMode = SubsequentExecutionDefault()
    static let gapSize = IntDefault(key: "gapSize", defaultValue: 0)
    static let sizeOffset = FloatDefault(key: "sizeOffset", defaultValue: 30)
    static let curtainChangeSize = OptionalBoolDefault(key: "curtainChangeSize")

    // MARK: - Screen Edge Gaps
    static let screenEdgeGapTop = FloatDefault(key: "screenEdgeGapTop", defaultValue: 0)
    static let screenEdgeGapBottom = FloatDefault(key: "screenEdgeGapBottom", defaultValue: 0)
    static let screenEdgeGapLeft = FloatDefault(key: "screenEdgeGapLeft", defaultValue: 0)
    static let screenEdgeGapRight = FloatDefault(key: "screenEdgeGapRight", defaultValue: 0)
    static let screenEdgeGapsOnMainScreenOnly = OptionalBoolDefault(
        key: "screenEdgeGapsOnMainScreenOnly")

    // MARK: - Almost Maximize
    static let almostMaximizeHeight = FloatDefault(key: "almostMaximizeHeight", defaultValue: 0)
    static let almostMaximizeWidth = FloatDefault(key: "almostMaximizeWidth", defaultValue: 0)

    // MARK: - Minimum Window Size
    static let minimumWindowWidth = FloatDefault(key: "minimumWindowWidth", defaultValue: 0)
    static let minimumWindowHeight = FloatDefault(key: "minimumWindowHeight", defaultValue: 0)

    // MARK: - Cursor Movement
    static let moveCursor = OptionalBoolDefault(key: "moveCursor")
    static let moveCursorAcrossDisplays = OptionalBoolDefault(key: "moveCursorAcrossDisplays")

    // MARK: - Snapping
    static let windowSnapping = OptionalBoolDefault(key: "windowSnapping")
    static let snapEdgeMarginTop = IntDefault(key: "snapEdgeMarginTop", defaultValue: 5)
    static let snapEdgeMarginBottom = IntDefault(key: "snapEdgeMarginBottom", defaultValue: 5)
    static let snapEdgeMarginLeft = IntDefault(key: "snapEdgeMarginLeft", defaultValue: 5)
    static let snapEdgeMarginRight = IntDefault(key: "snapEdgeMarginRight", defaultValue: 5)

    // MARK: - Traversal
    static let traverseSingleScreen = OptionalBoolDefault(key: "traverseSingleScreen")

    // MARK: - Todo (Stage Manager)
    static let todo = OptionalBoolDefault(key: "todo")
    static let alwaysAccountForStage = OptionalBoolDefault(key: "alwaysAccountForStage")

    // MARK: - Center Half Cycles
    static let centerHalfCycles = OptionalBoolDefault(key: "centerHalfCycles")

    // MARK: - Stage Size
    static let stageSize = FloatDefault(key: "stageSize", defaultValue: 190)

    // MARK: - Todo Sidebar
    static let todoSidebarWidth = FloatDefault(key: "todoSidebarWidth", defaultValue: 400)
    static let todoMode = BoolDefault(key: "todoMode")
    static let todoApplication = StringDefault(key: "todoApplication", defaultValue: "")
    static let todoSidebarSide = TodoSidebarSideDefault()

    // MARK: - Footprint
    static let footprintAlpha = FloatDefault(key: "footprintAlpha", defaultValue: 0.3)
    static let footprintBorderWidth = FloatDefault(key: "footprintBorderWidth", defaultValue: 2.0)
    static let footprintFade = BoolDefault(key: "footprintFade")

    // MARK: - Restore
    static let unsnapRestore = OptionalBoolDefault(key: "unsnapRestore")

    // MARK: - Directional Move
    static let centeredDirectionalMove = OptionalBoolDefault(key: "centeredDirectionalMove")
    static let resizeOnDirectionalMove = OptionalBoolDefault(key: "resizeOnDirectionalMove")

    // MARK: - Helper Methods

    static func loadFromSupportDir() {
        // Placeholder for loading from support directory
        // In Rectangle, this loads from a plist file
        // For OhSnap, we'll use UserDefaults
    }
}
