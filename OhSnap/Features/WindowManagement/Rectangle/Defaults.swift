//
//  Defaults.swift
//  Rectangle Integration for OhSnap
//
//  Simplified defaults system for Rectangle functionality
//

import Foundation

// MARK: - Default Value Protocol

protocol DefaultValue {
    var enabled: Bool { get }
    var userEnabled: Bool { get }
    var value: Any { get }
}

// MARK: - Default Implementation

class Default<T>: DefaultValue {
    let key: String
    let defaultValue: T

    init(key: String, defaultValue: T) {
        self.key = key
        self.defaultValue = defaultValue
    }

    var enabled: Bool {
        return UserDefaults.standard.object(forKey: key) != nil
            ? UserDefaults.standard.bool(forKey: key) : (defaultValue as? Bool ?? true)
    }

    var userEnabled: Bool {
        return enabled
    }

    var value: Any {
        if let storedValue = UserDefaults.standard.object(forKey: key) {
            return storedValue
        }
        return defaultValue
    }

    var intValue: Int {
        return UserDefaults.standard.object(forKey: key) != nil
            ? UserDefaults.standard.integer(forKey: key) : (defaultValue as? Int ?? 0)
    }

    var cgFloat: CGFloat {
        return CGFloat(intValue)
    }

    var floatValue: Float {
        return UserDefaults.standard.object(forKey: key) != nil
            ? UserDefaults.standard.float(forKey: key) : (defaultValue as? Float ?? 0.0)
    }
}

// MARK: - SubsequentExecutionMode

enum SubsequentExecutionMode: Int {
    case resize = 0
    case acrossMonitor = 1
    case none = 2
    case acrossAndResize = 3
    case cycleMonitor = 4

    var resizes: Bool {
        switch self {
        case .resize, .acrossAndResize:
            return true
        default:
            return false
        }
    }
}

// MARK: - Defaults Container

class Defaults {

    // MARK: - Window Management
    static let subsequentExecutionMode = Default(
        key: "subsequentExecutionMode", defaultValue: SubsequentExecutionMode.resize.rawValue)
    static let gapSize = Default(key: "gapSize", defaultValue: 0)
    static let sizeOffset = Default(key: "sizeOffset", defaultValue: 30)
    static let curtainChangeSize = Default(key: "curtainChangeSize", defaultValue: false)

    // MARK: - Screen Edge Gaps
    static let screenEdgeGapTop = Default(key: "screenEdgeGapTop", defaultValue: 0)
    static let screenEdgeGapBottom = Default(key: "screenEdgeGapBottom", defaultValue: 0)
    static let screenEdgeGapLeft = Default(key: "screenEdgeGapLeft", defaultValue: 0)
    static let screenEdgeGapRight = Default(key: "screenEdgeGapRight", defaultValue: 0)
    static let screenEdgeGapsOnMainScreenOnly = Default(
        key: "screenEdgeGapsOnMainScreenOnly", defaultValue: false)

    // MARK: - Almost Maximize
    static let almostMaximizeHeight = Default(key: "almostMaximizeHeight", defaultValue: 0.9)
    static let almostMaximizeWidth = Default(key: "almostMaximizeWidth", defaultValue: 0.9)

    // MARK: - Minimum Window Size
    static let minimumWindowWidth = Default(key: "minimumWindowWidth", defaultValue: 0.0)
    static let minimumWindowHeight = Default(key: "minimumWindowHeight", defaultValue: 0.0)

    // MARK: - Cursor Movement
    static let moveCursor = Default(key: "moveCursor", defaultValue: false)
    static let moveCursorAcrossDisplays = Default(
        key: "moveCursorAcrossDisplays", defaultValue: false)

    // MARK: - Snapping
    static let windowSnapping = Default(key: "windowSnapping", defaultValue: true)
    static let snapEdgeMarginTop = Default(key: "snapEdgeMarginTop", defaultValue: 5)
    static let snapEdgeMarginBottom = Default(key: "snapEdgeMarginBottom", defaultValue: 5)
    static let snapEdgeMarginLeft = Default(key: "snapEdgeMarginLeft", defaultValue: 5)
    static let snapEdgeMarginRight = Default(key: "snapEdgeMarginRight", defaultValue: 5)

    // MARK: - Traversal
    static let traverseSingleScreen = Default(key: "traverseSingleScreen", defaultValue: false)

    // MARK: - Todo (Stage Manager)
    static let todo = Default(key: "todo", defaultValue: false)
    static let alwaysAccountForStage = Default(key: "alwaysAccountForStage", defaultValue: false)

    // MARK: - Center Half Cycles
    static let centerHalfCycles = Default(key: "centerHalfCycles", defaultValue: false)

    // MARK: - Stage Size
    static let stageSize = Default(key: "stageSize", defaultValue: 0.0)

    // MARK: - Footprint
    static let footprintAlpha = Default(key: "footprintAlpha", defaultValue: 0.3)
    static let footprintBorderWidth = Default(key: "footprintBorderWidth", defaultValue: 2.0)
    static let footprintFade = Default(key: "footprintFade", defaultValue: true)

    // MARK: - Restore
    static let unsnapRestore = Default(key: "unsnapRestore", defaultValue: false)

    // MARK: - Directional Move
    static let centeredDirectionalMove = Default(
        key: "centeredDirectionalMove", defaultValue: false)
    static let resizeOnDirectionalMove = Default(
        key: "resizeOnDirectionalMove", defaultValue: false)

    // MARK: - Helper Methods

    static func loadFromSupportDir() {
        // Placeholder for loading from support directory
        // In Rectangle, this loads from a plist file
        // For OhSnap, we'll use UserDefaults
    }
}

// MARK: - Extensions

extension Default where T == Int {
    var value: Int {
        return intValue
    }
}

extension Default where T == Float {
    var value: Float {
        return floatValue
    }
}

extension Default where T == Bool {
    var value: Bool {
        return enabled
    }
}

// MARK: - SubsequentExecutionMode Extension

extension Defaults {
    static var subsequentExecutionModeValue: SubsequentExecutionMode {
        return SubsequentExecutionMode(rawValue: subsequentExecutionMode.intValue) ?? .resize
    }
}

extension Default where T == Int {
    var subsequentExecutionModeValue: SubsequentExecutionMode {
        return SubsequentExecutionMode(rawValue: intValue) ?? .resize
    }

    var value: SubsequentExecutionMode {
        return SubsequentExecutionMode(rawValue: intValue) ?? .resize
    }
}
